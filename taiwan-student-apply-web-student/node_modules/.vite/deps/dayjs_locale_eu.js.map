{"version": 3, "sources": ["../../.pnpm/dayjs@1.10.5/node_modules/dayjs/locale/eu.js"], "sourcesContent": ["!function(a,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=e(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],e):(a=\"undefined\"!=typeof globalThis?globalThis:a||self).dayjs_locale_eu=e(a.dayjs)}(this,(function(a){\"use strict\";function e(a){return a&&\"object\"==typeof a&&\"default\"in a?a:{default:a}}var t=e(a),l={name:\"eu\",weekdays:\"igandea_astelehena_asteartea_asteazkena_osteguna_ostirala_larunbata\".split(\"_\"),months:\"urtarrila_otsaila_martxoa_apirila_maiatza_ekaina_uztaila_abuztua_iraila_urria_azaroa_abendua\".split(\"_\"),weekStart:1,weekdaysShort:\"ig._al._ar._az._og._ol._lr.\".split(\"_\"),monthsShort:\"urt._ots._mar._api._mai._eka._uzt._abu._ira._urr._aza._abe.\".split(\"_\"),weekdaysMin:\"ig_al_ar_az_og_ol_lr\".split(\"_\"),ordinal:function(a){return a},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"YYYY-MM-DD\",LL:\"YYYY[ko] MMMM[ren] D[a]\",LLL:\"YYYY[ko] MMMM[ren] D[a] HH:mm\",LLLL:\"dddd, YYYY[ko] MMMM[ren] D[a] HH:mm\",l:\"YYYY-M-D\",ll:\"YYYY[ko] MMM D[a]\",lll:\"YYYY[ko] MMM D[a] HH:mm\",llll:\"ddd, YYYY[ko] MMM D[a] HH:mm\"},relativeTime:{future:\"%s barru\",past:\"duela %s\",s:\"segundo batzuk\",m:\"minutu bat\",mm:\"%d minutu\",h:\"ordu bat\",hh:\"%d ordu\",d:\"egun bat\",dd:\"%d egun\",M:\"hilabete bat\",MM:\"%d hilabete\",y:\"urte bat\",yy:\"%d urte\"}};return t.default.locale(l,null,!0),l}));"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,mBAAgB,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,OAAO,GAAE,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,kBAAgB,EAAE,EAAE,KAAK;AAAA,IAAC,EAAE,SAAM,SAAS,GAAE;AAAC;AAAa,eAAS,EAAEA,IAAE;AAAC,eAAOA,MAAG,YAAU,OAAOA,MAAG,aAAYA,KAAEA,KAAE,EAAC,SAAQA,GAAC;AAAA,MAAC;AAAC,UAAI,IAAE,EAAE,CAAC,GAAE,IAAE,EAAC,MAAK,MAAK,UAAS,sEAAsE,MAAM,GAAG,GAAE,QAAO,+FAA+F,MAAM,GAAG,GAAE,WAAU,GAAE,eAAc,8BAA8B,MAAM,GAAG,GAAE,aAAY,8DAA8D,MAAM,GAAG,GAAE,aAAY,uBAAuB,MAAM,GAAG,GAAE,SAAQ,SAASA,IAAE;AAAC,eAAOA;AAAA,MAAC,GAAE,SAAQ,EAAC,IAAG,SAAQ,KAAI,YAAW,GAAE,cAAa,IAAG,2BAA0B,KAAI,iCAAgC,MAAK,uCAAsC,GAAE,YAAW,IAAG,qBAAoB,KAAI,2BAA0B,MAAK,+BAA8B,GAAE,cAAa,EAAC,QAAO,YAAW,MAAK,YAAW,GAAE,kBAAiB,GAAE,cAAa,IAAG,aAAY,GAAE,YAAW,IAAG,WAAU,GAAE,YAAW,IAAG,WAAU,GAAE,gBAAe,IAAG,eAAc,GAAE,YAAW,IAAG,UAAS,EAAC;AAAE,aAAO,EAAE,QAAQ,OAAO,GAAE,MAAK,IAAE,GAAE;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": ["a"]}