{"version": 3, "sources": ["../../.pnpm/vue@3.4.27/node_modules/vue/dist/vue.runtime.esm-bundler.js"], "sourcesContent": ["/**\n* vue v3.4.27\n* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors\n* @license MIT\n**/\nimport { initCustomFormatter, warn } from '@vue/runtime-dom';\nexport * from '@vue/runtime-dom';\n\nfunction initDev() {\n  {\n    initCustomFormatter();\n  }\n}\n\nif (!!(process.env.NODE_ENV !== \"production\")) {\n  initDev();\n}\nconst compile = () => {\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    warn(\n      `Runtime compilation is not supported in this build of Vue.` + (` Configure your bundler to alias \"vue\" to \"vue/dist/vue.esm-bundler.js\".` )\n    );\n  }\n};\n\nexport { compile };\n"], "mappings": ";;;;;;;AAKA;AACA;AAEA,SAAS,UAAU;AACjB;AACE,wBAAoB;AAAA,EACtB;AACF;AAEA,IAAI,MAA2C;AAC7C,UAAQ;AACV;AACA,IAAM,UAAU,MAAM;AACpB,MAAI,MAA2C;AAC7C;AAAA,MACE;AAAA,IACF;AAAA,EACF;AACF;", "names": []}