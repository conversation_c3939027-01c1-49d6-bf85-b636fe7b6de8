import {
  require_dayjs_min
} from "./chunk-Z76YB3AZ.js";
import {
  __commonJS
} from "./chunk-GFT2G5UO.js";

// node_modules/.pnpm/dayjs@1.10.5/node_modules/dayjs/locale/eu.js
var require_eu = __commonJS({
  "node_modules/.pnpm/dayjs@1.10.5/node_modules/dayjs/locale/eu.js"(exports, module) {
    !function(a, e) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = e(require_dayjs_min()) : "function" == typeof define && define.amd ? define(["dayjs"], e) : (a = "undefined" != typeof globalThis ? globalThis : a || self).dayjs_locale_eu = e(a.dayjs);
    }(exports, function(a) {
      "use strict";
      function e(a2) {
        return a2 && "object" == typeof a2 && "default" in a2 ? a2 : { default: a2 };
      }
      var t = e(a), l = { name: "eu", weekdays: "igandea_astelehena_asteartea_asteazkena_osteguna_ostirala_larunbata".split("_"), months: "urtarrila_otsaila_martxoa_apirila_maiatza_ekaina_uztaila_abuztua_iraila_urria_azaroa_abendua".split("_"), weekStart: 1, weekdaysShort: "ig._al._ar._az._og._ol._lr.".split("_"), monthsShort: "urt._ots._mar._api._mai._eka._uzt._abu._ira._urr._aza._abe.".split("_"), weekdaysMin: "ig_al_ar_az_og_ol_lr".split("_"), ordinal: function(a2) {
        return a2;
      }, formats: { LT: "HH:mm", LTS: "HH:mm:ss", L: "YYYY-MM-DD", LL: "YYYY[ko] MMMM[ren] D[a]", LLL: "YYYY[ko] MMMM[ren] D[a] HH:mm", LLLL: "dddd, YYYY[ko] MMMM[ren] D[a] HH:mm", l: "YYYY-M-D", ll: "YYYY[ko] MMM D[a]", lll: "YYYY[ko] MMM D[a] HH:mm", llll: "ddd, YYYY[ko] MMM D[a] HH:mm" }, relativeTime: { future: "%s barru", past: "duela %s", s: "segundo batzuk", m: "minutu bat", mm: "%d minutu", h: "ordu bat", hh: "%d ordu", d: "egun bat", dd: "%d egun", M: "hilabete bat", MM: "%d hilabete", y: "urte bat", yy: "%d urte" } };
      return t.default.locale(l, null, true), l;
    });
  }
});
export default require_eu();
//# sourceMappingURL=dayjs_locale_eu.js.map
