hoistPattern:
  - '*'
hoistedDependencies:
  '@ant-design/colors@6.0.0':
    '@ant-design/colors': private
  '@ant-design/icons-svg@4.4.2':
    '@ant-design/icons-svg': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/parser@7.27.5':
    '@babel/parser': private
  '@babel/runtime@7.27.6':
    '@babel/runtime': private
  '@babel/types@7.27.6':
    '@babel/types': private
  '@ctrl/tinycolor@3.6.1':
    '@ctrl/tinycolor': private
  '@emotion/hash@0.9.2':
    '@emotion/hash': private
  '@emotion/unitless@0.8.1':
    '@emotion/unitless': private
  '@esbuild/aix-ppc64@0.20.2':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.20.2':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.20.2':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.20.2':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.20.2':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.20.2':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.20.2':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.20.2':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.20.2':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.20.2':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.20.2':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.20.2':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.20.2':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.20.2':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.20.2':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.20.2':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.20.2':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-x64@0.20.2':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-x64@0.20.2':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.20.2':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.20.2':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.20.2':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.20.2':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@8.57.1)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/eslintrc@2.1.4':
    '@eslint/eslintrc': private
  '@eslint/js@8.57.1':
    '@eslint/js': private
  '@humanwhocodes/config-array@0.13.0':
    '@humanwhocodes/config-array': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/object-schema@2.0.3':
    '@humanwhocodes/object-schema': private
  '@intlify/core-base@9.13.1':
    '@intlify/core-base': private
  '@intlify/message-compiler@9.13.1':
    '@intlify/message-compiler': private
  '@intlify/shared@9.13.1':
    '@intlify/shared': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/source-map@0.3.6':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@pkgr/core@0.1.2':
    '@pkgr/core': private
  '@rollup/rollup-android-arm-eabi@4.44.0':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.44.0':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.44.0':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.44.0':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.44.0':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.44.0':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.44.0':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.44.0':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.44.0':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.44.0':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.44.0':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.44.0':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.44.0':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.44.0':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.44.0':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.44.0':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.44.0':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.44.0':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.44.0':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.44.0':
    '@rollup/rollup-win32-x64-msvc': private
  '@simonwep/pickr@1.8.2':
    '@simonwep/pickr': private
  '@types/eslint-scope@3.7.7':
    '@types/eslint-scope': private
  '@types/eslint@9.6.1':
    '@types/eslint': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/minimist@1.2.5':
    '@types/minimist': private
  '@types/node@24.0.3':
    '@types/node': private
  '@types/normalize-package-data@2.4.4':
    '@types/normalize-package-data': private
  '@types/parse-json@4.0.2':
    '@types/parse-json': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@vue/compiler-core@3.4.27':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.4.27':
    '@vue/compiler-dom': private
  '@vue/compiler-ssr@3.4.27':
    '@vue/compiler-ssr': private
  '@vue/devtools-api@6.6.4':
    '@vue/devtools-api': private
  '@vue/reactivity@3.4.27':
    '@vue/reactivity': private
  '@vue/runtime-core@3.4.27':
    '@vue/runtime-core': private
  '@vue/runtime-dom@3.4.27':
    '@vue/runtime-dom': private
  '@vue/server-renderer@3.4.27(vue@3.4.27)':
    '@vue/server-renderer': private
  '@vue/shared@3.4.27':
    '@vue/shared': private
  '@webassemblyjs/ast@1.14.1':
    '@webassemblyjs/ast': private
  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    '@webassemblyjs/floating-point-hex-parser': private
  '@webassemblyjs/helper-api-error@1.13.2':
    '@webassemblyjs/helper-api-error': private
  '@webassemblyjs/helper-buffer@1.14.1':
    '@webassemblyjs/helper-buffer': private
  '@webassemblyjs/helper-numbers@1.13.2':
    '@webassemblyjs/helper-numbers': private
  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    '@webassemblyjs/helper-wasm-bytecode': private
  '@webassemblyjs/helper-wasm-section@1.14.1':
    '@webassemblyjs/helper-wasm-section': private
  '@webassemblyjs/ieee754@1.13.2':
    '@webassemblyjs/ieee754': private
  '@webassemblyjs/leb128@1.13.2':
    '@webassemblyjs/leb128': private
  '@webassemblyjs/utf8@1.13.2':
    '@webassemblyjs/utf8': private
  '@webassemblyjs/wasm-edit@1.14.1':
    '@webassemblyjs/wasm-edit': private
  '@webassemblyjs/wasm-gen@1.14.1':
    '@webassemblyjs/wasm-gen': private
  '@webassemblyjs/wasm-opt@1.14.1':
    '@webassemblyjs/wasm-opt': private
  '@webassemblyjs/wasm-parser@1.14.1':
    '@webassemblyjs/wasm-parser': private
  '@webassemblyjs/wast-printer@1.14.1':
    '@webassemblyjs/wast-printer': private
  '@xtuc/ieee754@1.2.0':
    '@xtuc/ieee754': private
  '@xtuc/long@4.2.2':
    '@xtuc/long': private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn@8.15.0:
    acorn: private
  ajv-formats@2.1.1(ajv@8.17.1):
    ajv-formats: private
  ajv-keywords@5.1.0(ajv@8.17.1):
    ajv-keywords: private
  ajv@6.12.6:
    ajv: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  argparse@2.0.1:
    argparse: private
  array-tree-filter@2.1.0:
    array-tree-filter: private
  array-union@2.1.0:
    array-union: private
  arrify@1.0.1:
    arrify: private
  astral-regex@2.0.0:
    astral-regex: private
  async-validator@4.2.5:
    async-validator: private
  asynckit@0.4.0:
    asynckit: private
  balanced-match@2.0.0:
    balanced-match: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.0:
    browserslist: private
  buffer-from@1.1.2:
    buffer-from: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  callsites@3.1.0:
    callsites: private
  camelcase-keys@6.2.2:
    camelcase-keys: private
  camelcase@5.3.1:
    camelcase: private
  caniuse-lite@1.0.30001723:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  chrome-trace-event@1.0.4:
    chrome-trace-event: private
  clone-regexp@2.2.0:
    clone-regexp: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  colord@2.9.3:
    colord: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@2.20.3:
    commander: private
  compute-scroll-into-view@1.0.20:
    compute-scroll-into-view: private
  concat-map@0.0.1:
    concat-map: private
  copy-anything@2.0.6:
    copy-anything: private
  core-js@3.43.0:
    core-js: private
  cosmiconfig@7.1.0:
    cosmiconfig: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-functions-list@3.2.3:
    css-functions-list: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  debug@4.4.1:
    debug: private
  decamelize-keys@1.1.1:
    decamelize-keys: private
  decamelize@1.2.0:
    decamelize: private
  deep-is@0.1.4:
    deep-is: private
  delayed-stream@1.0.0:
    delayed-stream: private
  dir-glob@3.0.1:
    dir-glob: private
  doctrine@3.0.0:
    doctrine: private
  dom-align@1.12.4:
    dom-align: private
  dom-scroll-into-view@2.0.1:
    dom-scroll-into-view: private
  dunder-proto@1.0.1:
    dunder-proto: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  electron-to-chromium@1.5.171:
    electron-to-chromium: private
  emoji-regex@8.0.0:
    emoji-regex: private
  enhanced-resolve@5.18.1:
    enhanced-resolve: private
  entities@4.5.0:
    entities: private
  errno@0.1.8:
    errno: private
  error-ex@1.3.2:
    error-ex: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  esbuild@0.20.2:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@7.2.2:
    eslint-scope: private
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: private
  espree@9.6.1:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@2.0.2:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  events@3.3.0:
    events: private
  execall@2.0.0:
    execall: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-diff@1.3.0:
    fast-diff: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-uri@3.0.6:
    fast-uri: private
  fastest-levenshtein@1.0.16:
    fastest-levenshtein: private
  fastq@1.19.1:
    fastq: private
  file-entry-cache@6.0.1:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@3.2.0:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  follow-redirects@1.15.9:
    follow-redirects: private
  foreground-child@3.3.1:
    foreground-child: private
  form-data@4.0.3:
    form-data: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-stdin@8.0.0:
    get-stdin: private
  glob-parent@6.0.2:
    glob-parent: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  glob@10.4.5:
    glob: private
  global-modules@2.0.0:
    global-modules: private
  global-prefix@3.0.0:
    global-prefix: private
  globals@13.24.0:
    globals: private
  globby@11.1.0:
    globby: private
  globjoin@0.1.4:
    globjoin: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  hard-rejection@2.1.0:
    hard-rejection: private
  has-flag@4.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  hosted-git-info@4.1.0:
    hosted-git-info: private
  html-tags@3.3.1:
    html-tags: private
  iconv-lite@0.6.3:
    iconv-lite: private
  ignore@5.3.2:
    ignore: private
  image-size@0.5.5:
    image-size: private
  import-fresh@3.3.1:
    import-fresh: private
  import-lazy@4.0.0:
    import-lazy: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@4.0.0:
    indent-string: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ini@1.3.8:
    ini: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-core-module@2.16.1:
    is-core-module: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-plain-obj@1.1.0:
    is-plain-obj: private
  is-plain-object@5.0.0:
    is-plain-object: private
  is-regexp@2.1.0:
    is-regexp: private
  is-what@3.14.1:
    is-what: private
  isexe@2.0.0:
    isexe: private
  jackspeak@3.4.3:
    jackspeak: private
  jest-worker@27.5.1:
    jest-worker: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsbn@1.1.0:
    jsbn: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  keyv@4.5.4:
    keyv: private
  kind-of@6.0.3:
    kind-of: private
  known-css-properties@0.25.0:
    known-css-properties: private
  levn@0.4.1:
    levn: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  loader-runner@4.3.0:
    loader-runner: private
  locate-path@6.0.0:
    locate-path: private
  lodash-es@4.17.21:
    lodash-es: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.truncate@4.4.2:
    lodash.truncate: private
  loose-envify@1.4.0:
    loose-envify: private
  lru-cache@10.4.3:
    lru-cache: private
  magic-string@0.30.17:
    magic-string: private
  make-dir@2.1.0:
    make-dir: private
  map-obj@4.3.0:
    map-obj: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mathml-tag-names@2.1.3:
    mathml-tag-names: private
  meow@9.0.0:
    meow: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@1.6.0:
    mime: private
  min-indent@1.0.1:
    min-indent: private
  minimatch@3.1.2:
    minimatch: private
  minimist-options@4.1.0:
    minimist-options: private
  minipass@7.1.2:
    minipass: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  nanopop@2.4.2:
    nanopop: private
  natural-compare@1.4.0:
    natural-compare: private
  needle@3.3.1:
    needle: private
  neo-async@2.6.2:
    neo-async: private
  node-releases@2.0.19:
    node-releases: private
  normalize-package-data@3.0.3:
    normalize-package-data: private
  normalize-path@3.0.0:
    normalize-path: private
  nth-check@2.1.1:
    nth-check: private
  once@1.4.0:
    once: private
  optionator@0.9.4:
    optionator: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-try@2.2.0:
    p-try: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  parent-module@1.0.1:
    parent-module: private
  parse-json@5.2.0:
    parse-json: private
  parse-node-version@1.0.1:
    parse-node-version: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-type@4.0.0:
    path-type: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pify@4.0.1:
    pify: private
  postcss-media-query-parser@0.2.3:
    postcss-media-query-parser: private
  postcss-resolve-nested-selector@0.1.6:
    postcss-resolve-nested-selector: private
  postcss-safe-parser@6.0.0(postcss@8.5.6):
    postcss-safe-parser: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss-sorting@7.0.1(postcss@8.5.6):
    postcss-sorting: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  postcss@8.5.6:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prettier-linter-helpers@1.0.0:
    prettier-linter-helpers: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  prr@1.0.1:
    prr: private
  punycode@2.3.1:
    punycode: private
  queue-microtask@1.2.3:
    queue-microtask: private
  quick-lru@4.0.1:
    quick-lru: private
  randombytes@2.1.0:
    randombytes: private
  read-pkg-up@7.0.1:
    read-pkg-up: private
  read-pkg@5.2.0:
    read-pkg: private
  redent@3.0.0:
    redent: private
  require-from-string@2.0.2:
    require-from-string: private
  resize-observer-polyfill@1.5.1:
    resize-observer-polyfill: private
  resolve-from@5.0.0:
    resolve-from: private
  resolve@1.22.10:
    resolve: private
  reusify@1.1.0:
    reusify: private
  rollup@4.44.0:
    rollup: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safer-buffer@2.1.2:
    safer-buffer: private
  sax@1.4.1:
    sax: private
  schema-utils@4.3.2:
    schema-utils: private
  scroll-into-view-if-needed@2.2.31:
    scroll-into-view-if-needed: private
  semver@7.7.2:
    semver: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  shallow-equal@1.2.1:
    shallow-equal: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  signal-exit@3.0.7:
    signal-exit: private
  slash@3.0.0:
    slash: private
  slice-ansi@4.0.0:
    slice-ansi: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  spdx-correct@3.2.0:
    spdx-correct: private
  spdx-exceptions@2.5.0:
    spdx-exceptions: private
  spdx-expression-parse@3.0.1:
    spdx-expression-parse: private
  spdx-license-ids@3.0.21:
    spdx-license-ids: private
  specificity@0.4.1:
    specificity: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-indent@3.0.0:
    strip-indent: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  style-search@0.1.0:
    style-search: private
  stylelint-config-recommended@7.0.0(stylelint@14.8.5):
    stylelint-config-recommended: private
  stylis@4.3.6:
    stylis: private
  supports-color@7.2.0:
    supports-color: private
  supports-hyperlinks@2.3.0:
    supports-hyperlinks: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  svg-tags@1.0.0:
    svg-tags: private
  synckit@0.8.8:
    synckit: private
  table@6.9.0:
    table: private
  tapable@2.2.2:
    tapable: private
  terser-webpack-plugin@5.3.14(webpack@5.99.9):
    terser-webpack-plugin: private
  text-table@0.2.0:
    text-table: private
  throttle-debounce@5.0.2:
    throttle-debounce: private
  to-regex-range@5.0.1:
    to-regex-range: private
  trim-newlines@3.0.1:
    trim-newlines: private
  tslib@2.8.1:
    tslib: private
  type-check@0.4.0:
    type-check: private
  type-fest@0.20.2:
    type-fest: private
  undici-types@7.8.0:
    undici-types: private
  update-browserslist-db@1.1.3(browserslist@4.25.0):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  util-deprecate@1.0.2:
    util-deprecate: private
  v8-compile-cache@2.4.0:
    v8-compile-cache: private
  validate-npm-package-license@3.0.4:
    validate-npm-package-license: private
  vue-demi@0.14.10(vue@3.4.27):
    vue-demi: private
  vue-types@3.0.2(vue@3.4.27):
    vue-types: private
  warning@4.0.3:
    warning: private
  watchpack@2.4.4:
    watchpack: private
  webpack-sources@3.3.2:
    webpack-sources: private
  webpack@5.99.9:
    webpack: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@8.1.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@4.0.2:
    write-file-atomic: private
  xml-name-validator@4.0.0:
    xml-name-validator: private
  yallist@4.0.0:
    yallist: private
  yaml@1.10.2:
    yaml: private
  yargs-parser@20.2.9:
    yargs-parser: private
  yocto-queue@0.1.0:
    yocto-queue: private
ignoredBuilds:
  - core-js
  - vue-demi
  - esbuild
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.0
pendingBuilds: []
prunedAt: Fri, 20 Jun 2025 12:49:55 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.20.2'
  - '@esbuild/android-arm64@0.20.2'
  - '@esbuild/android-arm@0.20.2'
  - '@esbuild/android-x64@0.20.2'
  - '@esbuild/darwin-x64@0.20.2'
  - '@esbuild/freebsd-arm64@0.20.2'
  - '@esbuild/freebsd-x64@0.20.2'
  - '@esbuild/linux-arm64@0.20.2'
  - '@esbuild/linux-arm@0.20.2'
  - '@esbuild/linux-ia32@0.20.2'
  - '@esbuild/linux-loong64@0.20.2'
  - '@esbuild/linux-mips64el@0.20.2'
  - '@esbuild/linux-ppc64@0.20.2'
  - '@esbuild/linux-riscv64@0.20.2'
  - '@esbuild/linux-s390x@0.20.2'
  - '@esbuild/linux-x64@0.20.2'
  - '@esbuild/netbsd-x64@0.20.2'
  - '@esbuild/openbsd-x64@0.20.2'
  - '@esbuild/sunos-x64@0.20.2'
  - '@esbuild/win32-arm64@0.20.2'
  - '@esbuild/win32-ia32@0.20.2'
  - '@esbuild/win32-x64@0.20.2'
  - '@rollup/rollup-android-arm-eabi@4.44.0'
  - '@rollup/rollup-android-arm64@4.44.0'
  - '@rollup/rollup-darwin-x64@4.44.0'
  - '@rollup/rollup-freebsd-arm64@4.44.0'
  - '@rollup/rollup-freebsd-x64@4.44.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.44.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.44.0'
  - '@rollup/rollup-linux-arm64-gnu@4.44.0'
  - '@rollup/rollup-linux-arm64-musl@4.44.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.44.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.44.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.44.0'
  - '@rollup/rollup-linux-riscv64-musl@4.44.0'
  - '@rollup/rollup-linux-s390x-gnu@4.44.0'
  - '@rollup/rollup-linux-x64-gnu@4.44.0'
  - '@rollup/rollup-linux-x64-musl@4.44.0'
  - '@rollup/rollup-win32-arm64-msvc@4.44.0'
  - '@rollup/rollup-win32-ia32-msvc@4.44.0'
  - '@rollup/rollup-win32-x64-msvc@4.44.0'
storeDir: /Volumes/Data/.pnpm-store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
