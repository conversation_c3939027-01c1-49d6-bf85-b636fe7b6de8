export const themeColors = [
  // 蓝色
  {
    primaryColor: '#1677ff',
    activeColor: '#0958d9',
    hoverColor: '#4096ff',
  },
  // 蓝色2
  {
    primaryColor: '#2F54EB',
    activeColor: '#1d39c4',
    hoverColor: '#597ef7',
  },
  // 绿色
  {
    primaryColor: '#00b96b',
    activeColor: '#00945b',
    hoverColor: '#20c77c',
  },

  // 红色
  {
    primaryColor: '#F5222D',
    activeColor: '#cf1322',
    hoverColor: '#ff4d4f',
  },
  // 青色
  {
    primaryColor: '#13c2c2',
    activeColor: '#08979c',
    hoverColor: '#36cfc9',
  },
  // 粉色
  {
    primaryColor: '#EB2F96',
    activeColor: '#c41d7f',
    hoverColor: '#f759ab',
  },
  // 紫色
  {
    primaryColor: '#722ED1',
    activeColor: '#531dab',
    hoverColor: '#9254de',
  },
  // 极光绿
  {
    primaryColor: '#52c41a',
    activeColor: '#389e0d',
    hoverColor: '#73d13d',
  },
  // 深绿
  {
    primaryColor: '#009688',
    activeColor: '#007069',
    hoverColor: '#1aa391',
  },
  // 橙色
  {
    primaryColor: '#fa541c',
    activeColor: '#d4380d',
    hoverColor: '#ff7a45',
  },
];
