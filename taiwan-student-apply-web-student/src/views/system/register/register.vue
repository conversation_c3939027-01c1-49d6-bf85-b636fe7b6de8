<template>
  <div class="register-container">
    <div class="register-form-wrapper">
      <div class="register-header">
        <h2>台湾学生申请系统 - 注册</h2>
        <p>请填写以下信息完成注册</p>
      </div>

      <a-form
        ref="registerFormRef"
        :model="registerForm"
        :rules="registerRules"
        layout="vertical"
        @finish="onRegister"
      >
        <!-- 邮箱 -->
        <a-form-item label="邮箱" name="email">
          <a-input
            v-model:value="registerForm.email"
            placeholder="请输入邮箱地址"
            size="large"
          >
            <template #prefix>
              <MailOutlined />
            </template>
          </a-input>
        </a-form-item>

        <!-- 邮箱验证码 -->
        <a-form-item label="邮箱验证码" name="emailCode">
          <a-input-group compact>
            <a-input
              v-model:value="registerForm.emailCode"
              placeholder="请输入邮箱验证码"
              size="large"
              style="width: calc(100% - 120px)"
            />
            <a-button
              :loading="sendingEmailCode"
              :disabled="emailCodeCountdown > 0"
              size="large"
              style="width: 120px"
              @click="sendEmailCode"
            >
              {{ emailCodeCountdown > 0 ? `${emailCodeCountdown}s` : '发送验证码' }}
            </a-button>
          </a-input-group>
        </a-form-item>

        <!-- 图形验证码 -->
        <a-form-item label="图形验证码" name="captchaCode">
          <a-input-group compact>
            <a-input
              v-model:value="registerForm.captchaCode"
              placeholder="请输入图形验证码"
              size="large"
              style="width: calc(100% - 120px)"
            />
            <div
              class="captcha-image"
              style="width: 120px; height: 40px; cursor: pointer"
              @click="refreshCaptcha"
            >
              <img
                v-if="captchaImage"
                :src="captchaImage"
                alt="验证码"
                style="width: 100%; height: 100%"
              />
            </div>
          </a-input-group>
        </a-form-item>

        <!-- 真实姓名 -->
        <a-form-item label="真实姓名" name="actualName">
          <a-input
            v-model:value="registerForm.actualName"
            placeholder="请输入真实姓名"
            size="large"
          >
            <template #prefix>
              <UserOutlined />
            </template>
          </a-input>
        </a-form-item>

        <!-- 手机号 -->
        <a-form-item label="手机号" name="phone">
          <a-input
            v-model:value="registerForm.phone"
            placeholder="请输入手机号"
            size="large"
          >
            <template #prefix>
              <PhoneOutlined />
            </template>
          </a-input>
        </a-form-item>

        <!-- 密码 -->
        <a-form-item label="密码" name="password">
          <a-input-password
            v-model:value="registerForm.password"
            placeholder="请输入密码"
            size="large"
          >
            <template #prefix>
              <LockOutlined />
            </template>
          </a-input-password>
        </a-form-item>

        <!-- 确认密码 -->
        <a-form-item label="确认密码" name="confirmPassword">
          <a-input-password
            v-model:value="registerForm.confirmPassword"
            placeholder="请再次输入密码"
            size="large"
          >
            <template #prefix>
              <LockOutlined />
            </template>
          </a-input-password>
        </a-form-item>

        <!-- 注册按钮 -->
        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            size="large"
            block
            :loading="registerLoading"
          >
            注册
          </a-button>
        </a-form-item>

        <!-- 登录链接 -->
        <div class="register-footer">
          <span>已有账号？</span>
          <a @click="goToLogin">立即登录</a>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { message } from 'ant-design-vue';
  import { MailOutlined, UserOutlined, PhoneOutlined, LockOutlined } from '@ant-design/icons-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { loginApi } from '/@/api/system/login-api';

  // 路由
  const router = useRouter();

  // 表单引用
  const registerFormRef = ref();

  // ------------------------------ 注册表单相关 ------------------------------

  // 注册表单数据
  const registerForm = reactive({
    email: '',
    emailCode: '',
    captchaCode: '',
    captchaUuid: '',
    actualName: '',
    phone: '',
    password: '',
    confirmPassword: '',
  });

  // 表单验证规则
  const registerRules = {
    email: [
      { required: true, message: '请输入邮箱地址' },
      { type: 'email', message: '邮箱格式不正确' },
    ],
    emailCode: [
      { required: true, message: '请输入邮箱验证码' },
      { pattern: /^\d{6}$/, message: '邮箱验证码为6位数字' },
    ],
    captchaCode: [{ required: true, message: '请输入图形验证码' }],
    actualName: [{ required: true, message: '请输入真实姓名' }],
    phone: [
      { required: true, message: '请输入手机号' },
      { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' },
    ],
    password: [
      { required: true, message: '请输入密码' },
      { min: 6, message: '密码长度至少6位' },
    ],
    confirmPassword: [
      { required: true, message: '请再次输入密码' },
      {
        validator: (rule, value) => {
          if (value && value !== registerForm.password) {
            return Promise.reject('两次输入的密码不一致');
          }
          return Promise.resolve();
        },
      },
    ],
  };

  // 注册加载状态
  const registerLoading = ref(false);

  // ------------------------------ 验证码相关 ------------------------------

  // 图形验证码
  const captchaImage = ref('');

  // 邮箱验证码发送状态
  const sendingEmailCode = ref(false);
  const emailCodeCountdown = ref(0);

  // 获取图形验证码
  async function refreshCaptcha() {
    try {
      const res = await loginApi.getCaptcha();
      captchaImage.value = res.data.captchaImageBase64;
      registerForm.captchaUuid = res.data.captchaUuid;
    } catch (error) {
      message.error('获取验证码失败');
    }
  }

  // 发送邮箱验证码
  async function sendEmailCode() {
    // 验证邮箱和图形验证码
    if (!registerForm.email) {
      message.error('请先输入邮箱地址');
      return;
    }
    if (!registerForm.captchaCode) {
      message.error('请先输入图形验证码');
      return;
    }

    sendingEmailCode.value = true;
    try {
      await loginApi.sendEmailCode({
        email: registerForm.email,
        captchaCode: registerForm.captchaCode,
        captchaUuid: registerForm.captchaUuid,
      });
      message.success('验证码已发送到您的邮箱');
      
      // 开始倒计时
      emailCodeCountdown.value = 60;
      const timer = setInterval(() => {
        emailCodeCountdown.value--;
        if (emailCodeCountdown.value <= 0) {
          clearInterval(timer);
        }
      }, 1000);
      
      // 刷新图形验证码
      await refreshCaptcha();
    } catch (error) {
      message.error(error.msg || '发送验证码失败');
      await refreshCaptcha();
    } finally {
      sendingEmailCode.value = false;
    }
  }

  // ------------------------------ 注册相关 ------------------------------

  // 注册
  async function onRegister() {
    registerLoading.value = true;
    try {
      await loginApi.register(registerForm);
      message.success('注册成功，请登录');
      router.push('/login');
    } catch (error) {
      message.error(error.msg || '注册失败');
      await refreshCaptcha();
    } finally {
      registerLoading.value = false;
    }
  }

  // 跳转到登录页面
  function goToLogin() {
    router.push('/login');
  }

  // ------------------------------ 生命周期 ------------------------------

  onMounted(() => {
    refreshCaptcha();
  });
</script>

<style scoped>
  .register-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .register-form-wrapper {
    width: 400px;
    padding: 40px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .register-header {
    text-align: center;
    margin-bottom: 30px;
  }

  .register-header h2 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 24px;
  }

  .register-header p {
    margin: 0;
    color: #666;
    font-size: 14px;
  }

  .register-footer {
    text-align: center;
    margin-top: 20px;
  }

  .register-footer a {
    color: #1890ff;
    cursor: pointer;
  }

  .captcha-image {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
  }
</style>
