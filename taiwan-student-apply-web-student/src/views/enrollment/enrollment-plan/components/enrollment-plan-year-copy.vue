<template>
  <a-modal title="年度复制" :open="visible" :width="500" :confirm-loading="confirmLoading" @ok="onSubmit" @cancel="onClose">
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
      <a-form-item label="源年度" name="fromYear">
        <a-select v-model:value="form.fromYear" placeholder="请选择源年度" style="width: 100%" @change="onFromYearChange">
          <a-select-option v-for="year in availableYears" :key="year" :value="year"> {{ year }}年 </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="目标年度" name="toYear">
        <a-input-number v-model:value="form.toYear" :min="2000" :max="2100" placeholder="请输入目标年度" style="width: 100%" />
      </a-form-item>

      <a-alert
        v-if="form.fromYear"
        :message="`将复制 ${form.fromYear} 年的所有招生计划到 ${form.toYear || '目标'} 年`"
        type="info"
        show-icon
        style="margin-top: 16px"
      />

      <a-alert message="注意：目标年度不能已存在招生计划数据，复制操作不可撤销" type="warning" show-icon style="margin-top: 8px" />
    </a-form>
  </a-modal>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { message } from 'ant-design-vue';
  import { enrollmentPlanApi } from '../../../../api/enrollment/enrollment-plan-api';

  // ----------------------- emit -----------------------
  const emit = defineEmits(['refresh']);

  // ----------------------- 响应式数据 -----------------------

  // 组件引用
  const formRef = ref();

  // 显示、隐藏操作的变量和方法
  const visible = ref(false);
  const confirmLoading = ref(false);

  // 可用年度列表
  const availableYears = ref([]);

  // 表单数据
  const formDefault = {
    fromYear: null,
    toYear: null,
  };

  const form = reactive({ ...formDefault });

  // 表单验证规则
  const rules = {
    fromYear: [{ required: true, message: '请选择源年度' }],
    toYear: [
      { required: true, message: '请输入目标年度' },
      { type: 'number', min: 2000, max: 2100, message: '年度必须在2000-2100之间' },
    ],
  };

  // ----------------------- 生命周期 -----------------------

  onMounted(() => {
    loadAvailableYears();
  });

  // ----------------------- 方法 -----------------------

  /**
   * 加载可用年度列表
   */
  async function loadAvailableYears() {
    try {
      const res = await enrollmentPlanApi.queryYearList();
      availableYears.value = res.data || [];
    } catch (error) {
      message.error('加载年度列表失败：' + error.message);
    }
  }

  /**
   * 源年度变化处理
   */
  function onFromYearChange(value) {
    if (value) {
      // 自动设置目标年度为源年度+1
      form.toYear = value + 1;
    }
  }

  /**
   * 显示弹窗
   */
  function showModal() {
    // 重置表单
    Object.assign(form, formDefault);

    // 刷新年度列表
    loadAvailableYears();

    visible.value = true;

    // 清除表单验证
    setTimeout(() => {
      formRef.value?.clearValidate();
    }, 100);
  }

  /**
   * 关闭弹窗
   */
  function onClose() {
    visible.value = false;
    confirmLoading.value = false;
    Object.assign(form, formDefault);
  }

  /**
   * 提交表单
   */
  function onSubmit() {
    formRef.value
      .validate()
      .then(async () => {
        if (form.fromYear === form.toYear) {
          message.error('源年度和目标年度不能相同');
          return;
        }

        confirmLoading.value = true;
        try {
          await enrollmentPlanApi.copyFromPreviousYear(form);
          message.success('年度复制成功');

          emit('refresh');
          onClose();
        } catch (error) {
          message.error('年度复制失败：' + error.message);
        } finally {
          confirmLoading.value = false;
        }
      })
      .catch((error) => {
        console.log('表单验证失败:', error);
        message.error('请检查表单数据');
      });
  }

  // ----------------------- 暴露方法 -----------------------

  defineExpose({
    showModal,
  });
</script>

<style scoped>
  .ant-form-item {
    margin-bottom: 16px;
  }
</style>
