<template>
  <a-modal
    :title="form.enrollmentPlanId ? '编辑招生计划' : '新增招生计划'"
    :open="visible"
    :width="600"
    :confirm-loading="confirmLoading"
    @ok="onSubmit"
    @cancel="onClose"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
      <a-form-item label="招生年度" name="year">
        <a-input-number
          v-model:value="form.year"
          :min="2000"
          :max="2100"
          :disabled="!!form.enrollmentPlanId"
          placeholder="请输入招生年度"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item label="科类" name="subjectType">
        <SmartEnumSelect v-model:value="form.subjectType" placeholder="请选择科类" style="width: 100%" enum-name="SUBJECT_TYPE_ENUM" />
      </a-form-item>

      <a-form-item label="专业名称" name="majorName">
        <a-input v-model:value="form.majorName" placeholder="请输入专业名称" :maxlength="200" show-count />
      </a-form-item>

      <a-form-item label="所属学院" name="collegeName">
        <a-input v-model:value="form.collegeName" placeholder="请输入所属学院" :maxlength="200" show-count />
      </a-form-item>

      <a-form-item label="学制" name="educationDuration">
        <a-input-number v-model:value="form.educationDuration" :min="1" :max="8" placeholder="请输入学制（年）" style="width: 100%" />
      </a-form-item>

      <a-form-item label="排序" name="sort">
        <a-input-number v-model:value="form.sort" :min="0" placeholder="请输入排序值" style="width: 100%" />
      </a-form-item>

      <a-form-item label="备注" name="remark">
        <a-textarea v-model:value="form.remark" placeholder="请输入备注" :maxlength="500" :rows="3" show-count />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { ref, reactive } from 'vue';
  import { message } from 'ant-design-vue';
  import { enrollmentPlanApi } from '/@/api/enrollment/enrollment-plan-api';
  import SmartEnumSelect from '/@/components/framework/smart-enum-select/index.vue';

  // ----------------------- emit -----------------------
  const emit = defineEmits(['refresh']);

  // ----------------------- 响应式数据 -----------------------

  // 组件引用
  const formRef = ref();

  // 显示、隐藏操作的变量和方法
  const visible = ref(false);
  const confirmLoading = ref(false);
  const currentRecord = ref(null);

  // 表单数据
  const formDefault = {
    enrollmentPlanId: null,
    year: null,
    subjectType: null,
    majorName: '',
    collegeName: '',
    educationDuration: null,
    sort: 0,
    remark: '',
  };

  const form = reactive({ ...formDefault });

  // 表单验证规则
  const rules = {
    year: [{ required: true, message: '请选择招生年度' }],
    subjectType: [{ required: true, message: '请选择科类' }],
    majorName: [
      { required: true, message: '请输入专业名称' },
      { max: 200, message: '专业名称长度不能超过200个字符' },
    ],
    collegeName: [
      { required: true, message: '请输入所属学院' },
      { max: 200, message: '所属学院长度不能超过200个字符' },
    ],
    educationDuration: [
      { required: true, message: '请选择学制' },
      { type: 'number', min: 1, max: 8, message: '学制必须在1-8年之间' },
    ],
    remark: [{ max: 500, message: '备注长度不能超过500个字符' }],
    sort: [{ type: 'number', min: 0, message: '排序不能小于0' }],
  };

  // ----------------------- 方法 -----------------------

  /**
   * 显示弹窗
   */
  function showModal(type, record, selectedYear) {
    currentRecord.value = record;

    // 重置表单
    Object.assign(form, formDefault);

    if (!record || !record.enrollmentPlanId) {
      // 新增时设置默认年度
      form.year = selectedYear || new Date().getFullYear();
    } else {
      // 编辑时填充数据
      Object.assign(form, record);
    }

    visible.value = true;

    // 清除表单验证
    setTimeout(() => {
      formRef.value?.clearValidate();
    }, 100);
  }

  /**
   * 关闭弹窗
   */
  function onClose() {
    visible.value = false;
    confirmLoading.value = false;
    currentRecord.value = null;
    Object.assign(form, formDefault);
  }

  /**
   * 提交表单
   */
  function onSubmit() {
    formRef.value
      .validate()
      .then(async () => {
        confirmLoading.value = true;
        try {
          if (!form.enrollmentPlanId) {
            await enrollmentPlanApi.add(form);
            message.success('新增成功');
          } else {
            await enrollmentPlanApi.update(form);
            message.success('更新成功');
          }

          emit('refresh');
          onClose();
        } catch (error) {
          message.error(`${!form.enrollmentPlanId ? '新增' : '更新'}失败：${error.message}`);
        } finally {
          confirmLoading.value = false;
        }
      })
      .catch((error) => {
        console.log('表单验证失败:', error);
        message.error('请检查表单数据');
      });
  }

  // ----------------------- 暴露方法 -----------------------

  defineExpose({
    showModal,
  });
</script>
