<template>
  <div class="height100">
    <a-row :gutter="16" class="height100">
      <!-- 左侧年度列表 -->
      <a-col :span="3">
        <div class="year-sidebar height100">
          <div class="year-header">
            <h3>招生年度</h3>
            <a-button type="primary" size="small" @click="showYearCopyModal">
              <template #icon>
                <CopyOutlined />
              </template>
              年度复制
            </a-button>
          </div>
          <div class="year-list">
            <div v-for="year in yearList" :key="year" :class="['year-item', { active: selectedYear === year }]" @click="selectYear(year)">
              <span class="year-text">{{ year }}年</span>
              <span class="year-count">{{ getYearCount(year) }}个专业</span>
            </div>
          </div>
        </div>
      </a-col>
      <!-- 右侧内容区域 -->
      <a-col :span="21" class="height100">
        <div class="enrollment-plan-box height100">
          <!-- 搜索栏 -->
          <div class="search-container">
            <a-form layout="inline" :model="queryForm">
              <a-form-item label="专业名称">
                <a-input v-model:value="queryForm.majorName" placeholder="请输入专业名称" allow-clear style="width: 200px" />
              </a-form-item>
              <a-form-item label="所属学院">
                <a-input v-model:value="queryForm.collegeName" placeholder="请输入所属学院" allow-clear style="width: 200px" />
              </a-form-item>
              <a-form-item label="科类">
                <SmartEnumSelect
                  v-model:value="queryForm.subjectType"
                  placeholder="请选择科类"
                  allow-clear
                  style="width: 120px"
                  enum-name="SUBJECT_TYPE_ENUM"
                />
              </a-form-item>
              <a-form-item>
                <a-button type="primary" @click="queryPage">
                  <template #icon>
                    <SearchOutlined />
                  </template>
                  查询
                </a-button>
                <a-button @click="resetQuery" style="margin-left: 8px">
                  <template #icon>
                    <ReloadOutlined />
                  </template>
                  重置
                </a-button>
              </a-form-item>
            </a-form>
          </div>

          <!-- 操作按钮栏 -->
          <div class="action-container">
            <div class="action-left">
              <a-button type="primary" @click="addEnrollmentPlan">
                <template #icon>
                  <PlusOutlined />
                </template>
                新增
              </a-button>
              <a-button type="primary" ghost @click="showImportModal" :disabled="!selectedYear">
                <template #icon>
                  <ImportOutlined />
                </template>
                导入
              </a-button>
              <a-button type="primary" ghost @click="exportExcel">
                <template #icon>
                  <ExportOutlined />
                </template>
                导出
              </a-button>
              <a-button danger @click="batchDelete" :disabled="selectedRowKeys.length === 0">
                <template #icon>
                  <DeleteOutlined />
                </template>
                批量删除
              </a-button>
            </div>
            <div class="action-right">
              <span v-if="selectedYear" class="year-info"> 当前年度：{{ selectedYear }}年 </span>
            </div>
          </div>

          <!-- 数据表格 -->
          <div class="table-container">
            <a-table
              :columns="tableColumns"
              :data-source="tableData"
              :loading="tableLoading"
              :pagination="false"
              :row-selection="rowSelection"
              row-key="enrollmentPlanId"
              size="middle"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'action'">
                  <a-space>
                    <a-button type="link" size="small" @click="editEnrollmentPlan(record)"> 编辑 </a-button>
                    <a-button type="link" size="small" @click="viewDetail(record)"> 详情 </a-button>
                    <a-popconfirm title="确定要删除这条记录吗？" @confirm="deleteEnrollmentPlan(record.enrollmentPlanId)">
                      <a-button type="link" size="small" danger> 删除 </a-button>
                    </a-popconfirm>
                  </a-space>
                </template>
              </template>
            </a-table>
          </div>

          <!-- 分页 -->
          <div class="pagination-container">
            <a-pagination
              showSizeChanger
              showQuickJumper
              show-less-items
              :pageSizeOptions="PAGE_SIZE_OPTIONS"
              :defaultPageSize="queryForm.pageSize"
              v-model:current="queryForm.pageNum"
              v-model:pageSize="queryForm.pageSize"
              :total="total"
              @change="queryPage"
              @showSizeChange="queryPage"
              :show-total="(total) => `共${total}条`"
            />
          </div>
        </div>
      </a-col>
    </a-row>

    <!-- 新增/编辑对话框 -->
    <EnrollmentPlanForm ref="enrollmentPlanFormRef" @refresh="queryPage" :selectedYear="selectedYear" />

    <!-- 详情对话框 -->
    <EnrollmentPlanDetail ref="enrollmentPlanDetailRef" />

    <!-- 年度复制对话框 -->
    <EnrollmentPlanYearCopy ref="enrollmentPlanYearCopyRef" @refresh="loadYearList" />

    <!-- 导入对话框 -->
    <EnrollmentPlanImport ref="enrollmentPlanImportRef" @refresh="queryPage" :selectedYear="selectedYear" />
  </div>
</template>

<script setup>
  import { reactive, ref, onMounted, computed } from 'vue';
  import { Modal, message } from 'ant-design-vue';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { enrollmentPlanApi } from '/@/api/enrollment/enrollment-plan-api';
  import EnrollmentPlanForm from './components/enrollment-plan-form.vue';
  import EnrollmentPlanDetail from './components/enrollment-plan-detail.vue';
  import EnrollmentPlanYearCopy from './components/enrollment-plan-year-copy.vue';
  import EnrollmentPlanImport from './components/enrollment-plan-import.vue';
  import SmartEnumSelect from '/@/components/framework/smart-enum-select/index.vue';
  import { SearchOutlined, ReloadOutlined, PlusOutlined, CopyOutlined, ExportOutlined, ImportOutlined, DeleteOutlined } from '@ant-design/icons-vue';

  // ----------------------- 响应式数据 -----------------------

  // 年度相关
  const yearList = ref([]);
  const selectedYear = ref(null);
  const yearCounts = ref({});

  // 查询表单
  const queryForm = reactive({
    year: null,
    subjectType: null,
    majorName: '',
    collegeName: '',
    pageNum: 1,
    pageSize: 10,
  });

  // 表格相关
  const tableData = ref([]);
  const tableLoading = ref(false);
  const total = ref(0);
  const selectedRowKeys = ref([]);

  // 组件引用
  const enrollmentPlanFormRef = ref();
  const enrollmentPlanDetailRef = ref();
  const enrollmentPlanYearCopyRef = ref();
  const enrollmentPlanImportRef = ref();

  // ----------------------- 计算属性 -----------------------

  // 表格列配置（添加操作列）
  const tableColumns = computed(() => [
    {
      title: '专业名称',
      dataIndex: 'majorName',
      key: 'majorName',
      width: 200,
      ellipsis: true,
    },
    {
      title: '所属学院',
      dataIndex: 'collegeName',
      key: 'collegeName',
      width: 180,
      ellipsis: true,
    },
    {
      title: '科类',
      dataIndex: 'subjectTypeName',
      key: 'subjectTypeName',
      width: 100,
    },
    {
      title: '学制',
      dataIndex: 'educationDuration',
      key: 'educationDuration',
      width: 80,
      customRender: ({ text }) => `${text}年`,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      width: 200,
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 180,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
    },
  ]);

  // 行选择配置
  const rowSelection = computed(() => ({
    selectedRowKeys: selectedRowKeys.value,
    onChange: (keys) => {
      selectedRowKeys.value = keys;
    },
  }));

  // ----------------------- 生命周期 -----------------------

  onMounted(() => {
    loadYearList();
    // 初始化时就查询一次数据，即使没有年度数据
    queryPage();
  });

  // ----------------------- 年度管理 -----------------------

  async function loadYearList() {
    try {
      const [yearRes, countRes] = await Promise.all([enrollmentPlanApi.queryYearList(), enrollmentPlanApi.countByYear()]);

      yearList.value = yearRes.data || [];
      yearCounts.value = countRes.data || {};

      // 默认选择第一个年度
      if (yearList.value.length > 0 && !selectedYear.value) {
        selectedYear.value = yearList.value[0];
        queryForm.year = selectedYear.value;
        queryPage();
      }
    } catch (error) {
      message.error('加载年度列表失败：' + error.message);
    }
  }

  function selectYear(year) {
    selectedYear.value = year;
    queryForm.year = year;
    queryForm.pageNum = 1;
    selectedRowKeys.value = [];
    queryPage();
  }

  function getYearCount(year) {
    return yearCounts.value[year] || 0;
  }

  // ----------------------- 查询数据 -----------------------

  async function queryPage() {
    try {
      tableLoading.value = true;
      // 如果有选择年度就使用选择的年度，否则查询所有数据
      queryForm.year = selectedYear.value || null;
      const res = await enrollmentPlanApi.queryPage(queryForm);
      tableData.value = res.data.list || [];
      total.value = res.data.total || 0;
    } catch (error) {
      message.error('查询失败：' + error.message);
    } finally {
      tableLoading.value = false;
    }
  }

  function resetQuery() {
    queryForm.subjectType = null;
    queryForm.majorName = '';
    queryForm.collegeName = '';
    queryForm.pageNum = 1;
    queryPage();
  }

  // ----------------------- 操作方法 -----------------------

  function addEnrollmentPlan() {
    // 如果没有选择年度，使用当前年度
    const currentYear = selectedYear.value || new Date().getFullYear();
    enrollmentPlanFormRef.value.showModal('add', null, currentYear);
  }

  function editEnrollmentPlan(record) {
    enrollmentPlanFormRef.value.showModal('edit', record, selectedYear.value);
  }

  function viewDetail(record) {
    enrollmentPlanDetailRef.value.showModal(record);
  }

  async function deleteEnrollmentPlan(id) {
    try {
      await enrollmentPlanApi.delete(id);
      message.success('删除成功');
      queryPage();
      loadYearList();
    } catch (error) {
      message.error('删除失败：' + error.message);
    }
  }

  function batchDelete() {
    if (selectedRowKeys.value.length === 0) {
      message.warning('请选择要删除的记录');
      return;
    }

    Modal.confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedRowKeys.value.length} 条记录吗？`,
      onOk: async () => {
        try {
          await enrollmentPlanApi.batchDelete(selectedRowKeys.value);
          message.success('批量删除成功');
          selectedRowKeys.value = [];
          queryPage();
          loadYearList();
        } catch (error) {
          message.error('批量删除失败：' + error.message);
        }
      },
    });
  }

  function showYearCopyModal() {
    enrollmentPlanYearCopyRef.value.showModal();
  }

  function showImportModal() {
    if (!selectedYear.value) {
      message.warning('请先选择年度');
      return;
    }
    enrollmentPlanImportRef.value.showModal();
  }

  async function exportExcel() {
    const params = { ...queryForm };
    await enrollmentPlanApi.exportExcel(params);
  }
</script>

<style scoped lang="less">
  .height100 {
    height: 100%;
  }

  .year-sidebar {
    background: #fff;
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .year-header {
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }
    }

    .year-list {
      flex: 1;
      overflow-y: auto;
      padding: 8px 0;

      .year-item {
        padding: 12px 16px;
        cursor: pointer;
        border-bottom: 1px solid #f5f5f5;
        transition: all 0.2s;
        display: flex;
        justify-content: space-between;
        align-items: center;

        &:hover {
          background-color: #f5f5f5;
        }

        &.active {
          background-color: #e6f7ff;
          border-right: 3px solid #1890ff;
          color: #1890ff;

          .year-count {
            color: #1890ff;
          }
        }

        .year-text {
          font-weight: 500;
          font-size: 14px;
        }

        .year-count {
          font-size: 12px;
          color: #8c8c8c;
          background: #f5f5f5;
          padding: 2px 8px;
          border-radius: 10px;
        }
      }
    }
  }

  .enrollment-plan-box {
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    overflow: hidden;

    .search-container {
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      background: #fafafa;
    }

    .action-container {
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .action-left {
        display: flex;
        gap: 8px;
      }

      .action-right {
        .year-info {
          color: #595959;
          font-weight: 500;
        }
      }
    }

    .table-container {
      flex: 1;
      padding: 16px;
      overflow: auto;
    }

    .pagination-container {
      padding: 16px;
      border-top: 1px solid #f0f0f0;
      background: #fafafa;
      display: flex;
      justify-content: flex-end;
    }
  }
</style>
