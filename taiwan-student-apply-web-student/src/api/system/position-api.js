/**
 * 职务表 api 封装
 *
 * @Author:    kaiyun
 * @Date:      2024-06-23 23:31:38
 * @Copyright  <a href="https://1024lab.net">1024创新实验室</a>
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const positionApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/position/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/position/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/position/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/position/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
    return postRequest('/position/batchDelete', idList);
  },

  /**
   * 查询列表  <AUTHOR>
   */
  queryList: () => {
    return getRequest('/position/queryList');
  },

};
