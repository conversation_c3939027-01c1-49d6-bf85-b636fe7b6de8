/*
 *  登录
 *
 * @Author:    1024创新实验室-主任：卓大
 * @Date:      2022-09-03 21:59:58
 * @Wechat:    zhuda1024
 * @Email:     <EMAIL>
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */
import { getRequest, postRequest } from '/@/lib/axios';

export const loginApi = {
  /**
   * 登录 <AUTHOR>
   */
  login: (param) => {
    return postRequest('/login', param);
  },

  /**
   * 退出登录 <AUTHOR>
   */
  logout: () => {
    return getRequest('/login/logout');
  },

  /**
   * 获取验证码 <AUTHOR>
   */
  getCaptcha: () => {
    return getRequest('/login/getCaptcha');
  },

  /**
   * 获取登录信息 <AUTHOR>
   */
  getLoginInfo: () => {
    return getRequest('/login/getLoginInfo');
  },

  /**
   * 获取邮箱登录验证码 <AUTHOR>
   */
  sendLoginEmailCode: (loginName) => {
    return getRequest(`/login/sendEmailCode/${loginName}`);
  },

  /**
   * 获取双因子登录标识 <AUTHOR>
   */
  getTwoFactorLoginFlag: () => {
    return getRequest('/login/getTwoFactorLoginFlag');
  },

  /**
   * 检查是否需要图形验证码 <AUTHOR>
   */
  checkNeedCaptcha: (loginName) => {
    return getRequest(`/login/checkNeedCaptcha/${loginName}`);
  },

  /**
   * 发送邮箱验证码 <AUTHOR>
   */
  sendEmailCode: (param) => {
    return postRequest('/login/send-email-code', param);
  },

  /**
   * 学生注册 <AUTHOR>
   */
  register: (param) => {
    return postRequest('/login/register', param);
  },
};
