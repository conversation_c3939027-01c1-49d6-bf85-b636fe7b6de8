/*
 *  招生计划管理
 *
 * @Author:    <PERSON><PERSON><PERSON>
 * @Date:      2025-06-20
 */
import { postRequest, getRequest, getDownload } from '/@/lib/axios';

export const enrollmentPlanApi = {
  /**
   * 分页查询招生计划 <AUTHOR>
   */
  queryPage: (params) => {
    return postRequest('/enrollmentPlan/queryPage', params);
  },

  /**
   * 新增招生计划
   */
  add: (data) => {
    return postRequest('/enrollmentPlan/add', data);
  },

  /**
   * 更新招生计划
   */
  update: (data) => {
    return postRequest('/enrollmentPlan/update', data);
  },

  /**
   * 删除招生计划
   */
  delete: (enrollmentPlanId) => {
    return postRequest(`/enrollmentPlan/delete/${enrollmentPlanId}`);
  },

  /**
   * 批量删除招生计划
   */
  batchDelete: (idList) => {
    return postRequest('/enrollmentPlan/batchDelete', idList);
  },

  /**
   * 查询招生计划详情
   */
  getDetail: (enrollmentPlanId) => {
    return getRequest(`/enrollmentPlan/detail/${enrollmentPlanId}`);
  },

  /**
   * 查询所有年度列表
   */
  queryYearList: () => {
    return getRequest('/enrollmentPlan/queryYearList');
  },

  /**
   * 根据年度统计招生计划数量
   */
  countByYear: () => {
    return getRequest('/enrollmentPlan/countByYear');
  },

  /**
   * 年度复制
   */
  copyFromPreviousYear: (data) => {
    return postRequest('/enrollmentPlan/copyFromPreviousYear', data);
  },

  /**
   * 导出Excel
   */
  exportExcel: (params) => {
    return getDownload('/enrollmentPlan/exportExcel', params);
  },

  /**
   * 下载导入模板
   */
  downloadTemplate: () => {
    return getDownload('/enrollmentPlan/downloadTemplate');
  },

  /**
   * 导入Excel
   */
  importExcel: (formData) => {
    return postRequest('/enrollmentPlan/importExcel', formData);
  },
};
