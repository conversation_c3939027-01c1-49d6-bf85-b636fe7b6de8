package cn.edu.xmut.tsa.student.module.system.student.controller;

import cn.edu.xmut.tsa.base.common.annoation.RequestUser;
import cn.edu.xmut.tsa.base.common.domain.ResponseDTO;
import cn.edu.xmut.tsa.student.module.system.login.domain.RequestStudent;
import cn.edu.xmut.tsa.student.module.system.student.domain.vo.StudentInfoVO;
import cn.edu.xmut.tsa.student.module.system.student.service.StudentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 学生信息控制器
 *
 * <AUTHOR>
 * @Date 2025-06-20 11:00:00
 */
@Slf4j
@Tag(name = "学生信息")
@RestController
public class StudentInfoController {

    @Resource
    private StudentService studentService;

    /**
     * 获取当前学生信息
     */
    @GetMapping("/student/info")
    @Operation(summary = "获取当前学生信息  <AUTHOR>
    public ResponseDTO<StudentInfoVO> getStudentInfo(@RequestUser RequestStudent requestStudent) {
        return studentService.getStudentInfo(requestStudent.getStudentId());
    }
}
