package cn.edu.xmut.tsa.student.module.system.login.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 发送邮箱验证码表单
 *
 * <AUTHOR>
 * @Date 2025-06-20 11:00:00
 */
@Data
@Schema(description = "发送邮箱验证码表单")
public class SendEmailCodeForm {

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 图形验证码
     */
    @Schema(description = "图形验证码")
    @NotBlank(message = "图形验证码不能为空")
    private String captchaCode;

    /**
     * 图形验证码UUID
     */
    @Schema(description = "图形验证码UUID")
    @NotBlank(message = "图形验证码UUID不能为空")
    private String captchaUuid;
}
