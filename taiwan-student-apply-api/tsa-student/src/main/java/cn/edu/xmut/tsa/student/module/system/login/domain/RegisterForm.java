package cn.edu.xmut.tsa.student.module.system.login.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 学生注册表单
 *
 * <AUTHOR>
 * @Date 2025-06-20 11:00:00
 */
@Data
@Schema(description = "学生注册表单")
public class RegisterForm {

    /**
     * 邮箱（作为登录名）
     */
    @Schema(description = "邮箱")
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 密码
     */
    @Schema(description = "密码")
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 确认密码
     */
    @Schema(description = "确认密码")
    @NotBlank(message = "确认密码不能为空")
    private String confirmPassword;

    /**
     * 邮箱验证码
     */
    @Schema(description = "邮箱验证码")
    @NotBlank(message = "邮箱验证码不能为空")
    @Pattern(regexp = "^\\d{6}$", message = "邮箱验证码格式不正确")
    private String emailCode;

    /**
     * 图形验证码
     */
    @Schema(description = "图形验证码")
    @NotBlank(message = "图形验证码不能为空")
    private String captchaCode;

    /**
     * 图形验证码UUID
     */
    @Schema(description = "图形验证码UUID")
    @NotBlank(message = "图形验证码UUID不能为空")
    private String captchaUuid;

    /**
     * 真实姓名
     */
    @Schema(description = "真实姓名")
    @NotBlank(message = "真实姓名不能为空")
    private String actualName;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
}
