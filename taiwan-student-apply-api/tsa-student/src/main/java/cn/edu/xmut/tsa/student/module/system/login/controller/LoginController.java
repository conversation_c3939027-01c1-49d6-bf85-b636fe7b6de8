package cn.edu.xmut.tsa.student.module.system.login.controller;

import cn.dev33.satoken.stp.StpUtil;
import cn.edu.xmut.tsa.base.common.annoation.NoNeedLogin;
import cn.edu.xmut.tsa.base.common.constant.RequestHeaderConst;
import cn.edu.xmut.tsa.base.common.domain.ResponseDTO;
import cn.edu.xmut.tsa.base.common.util.SmartRequestUtil;
import cn.edu.xmut.tsa.base.module.support.captcha.domain.CaptchaVO;
import cn.edu.xmut.tsa.student.module.system.login.domain.LoginForm;
import cn.edu.xmut.tsa.student.module.system.login.domain.LoginResultVO;
import cn.edu.xmut.tsa.student.module.system.login.domain.RegisterForm;
import cn.edu.xmut.tsa.student.module.system.login.domain.SendEmailCodeForm;
import cn.edu.xmut.tsa.student.module.system.login.service.LoginService;
import cn.edu.xmut.tsa.student.util.AdminRequestUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 员工登录
 *
 * <AUTHOR>
 * @Date 2021-12-15 21:05:46
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright <a href="https://1024lab.net">1024创新实验室</a>
 */
@RestController
public class LoginController {

    @Resource
    private LoginService loginService;

    @NoNeedLogin
    @PostMapping("/login")
    @Operation(summary = "登录 <AUTHOR>
    public ResponseDTO<LoginResultVO> login(@Valid @RequestBody LoginForm loginForm, HttpServletRequest request) {
        String ip = JakartaServletUtil.getClientIP(request);
        String userAgent = JakartaServletUtil.getHeaderIgnoreCase(request, RequestHeaderConst.USER_AGENT);
        return loginService.login(loginForm, ip, userAgent);
    }

    @GetMapping("/login/getLoginInfo")
    @Operation(summary = "获取登录结果信息  <AUTHOR>
    public ResponseDTO<LoginResultVO> getLoginInfo() {
        String tokenValue = StpUtil.getTokenValue();
        LoginResultVO loginResult = loginService.getLoginResult(AdminRequestUtil.getRequestUser(), tokenValue);
        loginResult.setToken(tokenValue);
        return ResponseDTO.ok(loginResult);
    }

    @Operation(summary = "退出登陆  <AUTHOR>
    @GetMapping("/login/logout")
    public ResponseDTO<String> logout() {
        return loginService.logout(SmartRequestUtil.getRequestUser());
    }

    @Operation(summary = "获取验证码  <AUTHOR>
    @GetMapping("/login/getCaptcha")
    @NoNeedLogin
    public ResponseDTO<CaptchaVO> getCaptcha() {
        return loginService.getCaptcha();
    }

    @Operation(summary = "发送邮箱验证码  <AUTHOR>
    @PostMapping("/login/send-email-code")
    @NoNeedLogin
    public ResponseDTO<String> sendEmailCode(@RequestBody @Valid SendEmailCodeForm sendEmailCodeForm) {
        return loginService.sendEmailCode(sendEmailCodeForm);
    }

    @Operation(summary = "学生注册  <AUTHOR>
    @PostMapping("/login/register")
    @NoNeedLogin
    public ResponseDTO<String> register(@RequestBody @Valid RegisterForm registerForm) {
        return loginService.register(registerForm);
    }

}
